/**
 * @fileoverview Extension configuration module main entry point
 * @description Provides the core API for extension configuration processing
 *
 * Following the design principles from the documentation:
 * - Single Source of Truth (SSoT): All configuration authority comes from extension.config.ts
 * - Configuration as Code (CaC): Leverage TypeScript type system for intelligent hints and static validation
 * - Separation of Concerns (SoC): This module is responsible for orchestration only
 * - Data and I/O separation: Core logic modules only process data and return data structures, no file system writes
 * - Composability: Functions are split into independent, reusable functions
 */

import path from 'path';
import { merge } from 'lodash-es';
import { createLogger } from '../utils/logger.js';
import { utils } from '../utils/index.js';
import {
  DEFAULT_VALUES,
  WEBSTORE_TO_CN_MAPPING,
  REQUIRED_FIELDS,
  ERROR_MESSAGES,
  SUPPORTED_VARIANT_TYPES,
  SUPPORTED_WEBSTORES,
  SUPPORTED_MANIFEST_VERSIONS,
} from './constants.js';

import type {
  UserExtensionConfig,
  VariantConfig,
  ProcessedExtensionConfig,
  ProcessedVariantConfig,
  ExtensionJson,
} from './types.js';

const logger = createLogger('ExtensionConfig');

/**
 * Main function to define and process extension configuration
 * This is the function that users call in their extension.config.ts files
 *
 * Following the documented processing logic:
 * 1. Traverse userConfig.variants array
 * 2. For each variant, use lodash-es/merge to merge global config with current variant config
 * 3. Normalization and auto-filling:
 *    - Write merged top-level manifestVersion and defaultLocale into its manifest object internally
 *    - Auto-calculate and fill variantTarget, format: {webstore}-mv{manifestVersion}-{variantType}
 *    - If variantChannel or webstoreCN is empty, auto-fill according to rules
 * 4. Validation:
 *    - Check if name and version are defined at the top level
 *    - Check required fields in each variant object (variantId, variantName, variantType, webstore)
 *    - Ensure generated variantTarget is unique to prevent build conflicts
 *    - If user filled i18n.locales, clear and warn because this field is auto-generated by script scanning
 */
export function defineExtensionConfig(userConfig: UserExtensionConfig): ProcessedExtensionConfig {
  logger.info('Processing extension configuration...');

  try {
    // Step 4: Validation - Check if name and version are defined at the top level
    validateUserConfig(userConfig);

    // Step 1: Traverse userConfig.variants array
    const processedVariants: ProcessedVariantConfig[] = [];
    const variantTargets = new Set<string>();

    for (const variant of userConfig.variants) {
      // Step 2: Use lodash-es/merge to merge global config with current variant config
      const processedVariant = processVariant(userConfig, variant);

      // Step 4: Ensure generated variantTarget is unique to prevent build conflicts
      if (variantTargets.has(processedVariant.variantTarget)) {
        throw new Error(ERROR_MESSAGES.DUPLICATE_VARIANT_TARGET(processedVariant.variantTarget));
      }
      variantTargets.add(processedVariant.variantTarget);

      processedVariants.push(processedVariant);
    }

    const result: ProcessedExtensionConfig = {
      name: userConfig.name,
      version: userConfig.version,
      variants: processedVariants,
    };

    logger.success(
      `Successfully processed configuration for ${userConfig.name} with ${processedVariants.length} variants`,
    );
    return result;
  } catch (error) {
    logger.error('Failed to process extension configuration:', error);
    throw error;
  }
}

/**
 * Validate user configuration according to documentation requirements
 */
function validateUserConfig(config: UserExtensionConfig): void {
  // Check required fields at top level (name and version)
  for (const field of REQUIRED_FIELDS.USER_CONFIG) {
    if (!config[field as keyof UserExtensionConfig]) {
      throw new Error(ERROR_MESSAGES.MISSING_REQUIRED_FIELD(field));
    }
  }

  // Validate version format
  if (!utils.validation.isValidSemver(config.version)) {
    throw new Error(`Invalid version format: ${config.version}. Must follow SemVer specification.`);
  }

  // Validate manifest version
  if (config.manifestVersion && !SUPPORTED_MANIFEST_VERSIONS.includes(config.manifestVersion)) {
    throw new Error(ERROR_MESSAGES.INVALID_MANIFEST_VERSION(config.manifestVersion));
  }

  // Validate variants
  if (!Array.isArray(config.variants) || config.variants.length === 0) {
    throw new Error('At least one variant must be defined');
  }

  // Validate each variant
  for (const variant of config.variants) {
    validateVariantConfig(variant);
  }
}

/**
 * Validate variant configuration according to documentation requirements
 */
function validateVariantConfig(variant: VariantConfig): void {
  // Check required fields in each variant object (variantId, variantName, variantType, webstore)
  for (const field of REQUIRED_FIELDS.VARIANT_CONFIG) {
    if (!variant[field as keyof VariantConfig]) {
      throw new Error(ERROR_MESSAGES.MISSING_REQUIRED_FIELD(field));
    }
  }

  // Validate variant type
  if (!SUPPORTED_VARIANT_TYPES.includes(variant.variantType)) {
    throw new Error(ERROR_MESSAGES.INVALID_VARIANT_TYPE(variant.variantType));
  }

  // Validate webstore
  if (!SUPPORTED_WEBSTORES.includes(variant.webstore)) {
    throw new Error(ERROR_MESSAGES.INVALID_WEBSTORE(variant.webstore));
  }

  // Validate manifest version if specified
  if (variant.manifestVersion && !SUPPORTED_MANIFEST_VERSIONS.includes(variant.manifestVersion)) {
    throw new Error(ERROR_MESSAGES.INVALID_MANIFEST_VERSION(variant.manifestVersion));
  }
}

/**
 * Process a single variant configuration following documentation requirements
 * Step 2: Use lodash-es/merge to merge global config with current variant config
 * Step 3: Normalization and auto-filling
 */
function processVariant(
  globalConfig: UserExtensionConfig,
  variantConfig: VariantConfig,
): ProcessedVariantConfig {
  // Step 2: Use lodash-es/merge to merge global config with current variant config
  const mergedConfig = merge(
    {
      name: globalConfig.name,
      version: globalConfig.version,
      manifestVersion: globalConfig.manifestVersion,
      defaultLocale: globalConfig.defaultLocale,
      measurementId: globalConfig.measurementId,
      i18n: globalConfig.i18n,
      manifest: globalConfig.manifest,
    },
    variantConfig,
  );

  // Step 3: Normalization and auto-filling

  // Fill in default values
  const manifestVersion = mergedConfig.manifestVersion ?? DEFAULT_VALUES.manifestVersion;
  const defaultLocale = mergedConfig.defaultLocale ?? DEFAULT_VALUES.defaultLocale;

  // Auto-calculate and fill variantTarget, format: {webstore}-mv{manifestVersion}-{variantType}
  const variantTarget = generateVariantTarget(variantConfig, manifestVersion);

  // If variantChannel or webstoreCN is empty, auto-fill according to rules
  const variantChannel = variantConfig.variantChannel ?? generateVariantChannel(variantConfig);
  const webstoreCN = variantConfig.webstoreCN ?? generateWebstoreCN(variantConfig.webstore);

  // Process i18n configuration
  const i18nConfig = {
    locales: [], // [Scanned and filled] - Will be filled by i18n module during file system scanning
    includes: mergedConfig.i18n?.includes || [],
    excludes: mergedConfig.i18n?.excludes || [],
    chromeLocalesOnly: mergedConfig.i18n?.chromeLocalesOnly || [],
    chromeMessagesOnly: mergedConfig.i18n?.chromeMessagesOnly || [],
  };

  // Write merged top-level manifestVersion and defaultLocale into its manifest object internally
  const manifestConfig: ProcessedVariantConfig['manifest'] = {
    ...mergedConfig.manifest,
    version: globalConfig.version,
    manifest_version: manifestVersion,
    default_locale: defaultLocale,
  };

  const result: ProcessedVariantConfig = {
    // Metadata inherited from global or self-defined
    name: globalConfig.name,
    version: globalConfig.version,

    // Normalized and filled Variant core fields
    variantId: variantConfig.variantId,
    variantName: variantConfig.variantName,
    variantType: variantConfig.variantType,
    variantChannel, // [Filled]
    variantTarget, // [Filled]
    webstore: variantConfig.webstore,
    webstoreCN, // [Filled]
    webstoreId: variantConfig.webstoreId,
    webstoreUrl: variantConfig.webstoreUrl,

    // Normalized and filled plugin core configuration
    manifestVersion, // [Determined]
    defaultLocale, // [Determined]
    measurementId: mergedConfig.measurementId,

    // Complete i18n configuration
    i18n: i18nConfig,

    // Complete, merged manifest object
    manifest: manifestConfig,
  };

  return result;
}

/**
 * Generate variant channel name according to documentation rules
 * Auto-fill rule: If variantType is 'offline', format is {webstore}_offline, otherwise {webstore}
 */
function generateVariantChannel(variant: VariantConfig): string {
  if (variant.variantType === 'offline') {
    return `${variant.webstore}_offline`;
  }
  return variant.webstore;
}

/**
 * Generate webstore CN code according to documentation mapping
 */
function generateWebstoreCN(webstore: string): string {
  return WEBSTORE_TO_CN_MAPPING[webstore] || ``;
}

/**
 * Generate variant target identifier according to documentation format
 * Format: {webstore}-mv{manifestVersion}-{variantType}
 */
function generateVariantTarget(variant: VariantConfig, manifestVersion: number): string {
  return `${variant.webstore}-mv${manifestVersion}-${variant.variantType}`;
}

/**
 * Process i18n configuration according to documentation requirements
 * If user filled i18n.locales, clear and warn because this field is auto-generated by script scanning
 */
function processI18nConfig() {}

/**
 * Process manifest configuration according to documentation requirements
 * Write merged top-level manifestVersion and defaultLocale into its manifest object internally
 */
function processManifestConfig() {}

/**
 * Generate extension.json data structure for a specific variant
 * This function is called by the build system to generate the final configuration
 *
 * Note: This follows the "Data and I/O separation" principle - it only returns data structure,
 * the actual file writing is handled by the build module
 */
export async function generateExtensionJson(
  variantConfig: ProcessedVariantConfig,
  options: {
    includeI18n?: boolean;
    includeManifest?: boolean;
  } = {},
): Promise<ExtensionJson> {
  logger.info(`Generating extension.json data for variant: ${variantConfig.variantTarget}`);

  const result: ExtensionJson = {
    name: variantConfig.name,
    version: variantConfig.version,
    manifestVersion: variantConfig.manifestVersion,
    defaultLocale: variantConfig.defaultLocale,
    measurementId: variantConfig.measurementId,
    variantId: variantConfig.variantId,
    variantName: variantConfig.variantName,
    variantType: variantConfig.variantType,
    variantChannel: variantConfig.variantChannel,
    variantTarget: variantConfig.variantTarget,
    webstore: variantConfig.webstore,
    webstoreCN: variantConfig.webstoreCN,
    webstoreId: variantConfig.webstoreId,
    webstoreUrl: variantConfig.webstoreUrl,
    // These will be populated by respective modules (i18n and manifest)
    i18n: {} as any,
    manifest: {} as any,
  };

  // Note: Following the separation of concerns, i18n and manifest processing
  // will be handled by their respective modules

  return result;
}

/**
 * Get available extensions in the project
 * This is a utility function for build tools
 */
export async function getAvailableExtensions(): Promise<string[]> {
  const extensionDirs = await utils.glob.findExtensionDirs();
  return extensionDirs.map((dir) => path.basename(dir));
}

/**
 * Load and process extension configuration from file
 * This function handles the dynamic import and processing
 */
export async function loadExtensionConfig(
  extensionName: string,
): Promise<ProcessedExtensionConfig> {
  const configPath = path.join(utils.paths.getExtensionDir(extensionName), 'extension.config.ts');

  if (!(await utils.fs.exists(configPath))) {
    throw new Error(`Extension configuration not found: ${configPath}`);
  }

  // Dynamic import of the configuration file
  const { pathToFileURL } = await import('url');
  const configModule = await import(pathToFileURL(configPath).href);

  if (!configModule.default) {
    throw new Error(`Extension configuration must export a default value: ${configPath}`);
  }

  // The config module should already be processed by defineExtensionConfig
  return configModule.default;
}

/**
 * Validate a processed extension configuration
 * This is used for additional validation after processing
 */
export function validateProcessedConfig(config: ProcessedExtensionConfig): void {
  // Basic validation
  if (!config.name || !config.version) {
    throw new Error('Processed config must have name and version');
  }

  if (!Array.isArray(config.variants) || config.variants.length === 0) {
    throw new Error('Processed config must have at least one variant');
  }

  // Validate each variant
  for (const variant of config.variants) {
    if (!variant.variantTarget || !variant.variantId) {
      throw new Error('Each variant must have variantTarget and variantId');
    }

    // Ensure all required fields are filled
    if (!variant.variantChannel || !variant.webstoreCN) {
      throw new Error('Each variant must have auto-filled variantChannel and webstoreCN');
    }

    // Ensure manifest has required fields
    if (!variant.manifest.manifest_version || !variant.manifest.default_locale) {
      throw new Error('Each variant manifest must have manifest_version and default_locale');
    }
  }
}

// Re-export types for convenience
export type {
  UserExtensionConfig,
  VariantConfig,
  ProcessedExtensionConfig,
  ProcessedVariantConfig,
  ExtensionJson,
} from './types.js';

// Re-export constants
export { DEFAULT_VALUES, WEBSTORE_TO_CN_MAPPING } from './constants.js';
